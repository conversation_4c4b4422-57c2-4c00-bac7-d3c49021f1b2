name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Check version
        run: |
          VERSION_TAG=${{ github.ref_name }}
          VERSION_TAG=${VERSION_TAG#v}
          PROJECT_VERSION=$(grep "^version=" gradle.properties | cut -d'=' -f2)
          if [[ "$PROJECT_VERSION" == "${VERSION_TAG}-SNAPSHOT" ]]; then
            echo "Version match: tag $VERSION_TAG matches project version $PROJECT_VERSION, proceeding with release"
          else
            echo "Version mismatch: tag $VERSION_TAG does not match project version $PROJECT_VERSION"
            exit 1
          fi

      - name: Set up JDK
        uses: actions/setup-java@v5
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'gradle'

      - name: Build
        run: ./gradlew build :jarinker-cli:installDist --no-daemon --stacktrace

      - name: Package distribution
        run: |
          cd jarinker-cli/build/install
          tar -czf jarinker-cli-${{ github.ref_name }}.tar.gz jarinker/
          zip -r jarinker-cli-${{ github.ref_name }}.zip jarinker/

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: jarinker-cli-distribution
          path: |
            jarinker-cli/build/install/jarinker-cli-${{ github.ref_name }}.tar.gz
            jarinker-cli/build/install/jarinker-cli-${{ github.ref_name }}.zip

  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [ build ]
    steps:
      - name: Check out the repo
        uses: actions/checkout@v5

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: jarinker-cli-distribution
          path: artifacts

      - name: Show downloaded files
        run: |
          echo "Downloaded artifacts:"
          ls -la artifacts/

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          name: "${{ github.ref_name }}"
          tag_name: "${{ github.ref_name }}"
          generate_release_notes: true
          draft: true
          prerelease: false
          files: artifacts/*
