name: Build
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Check out the repo
        uses: actions/checkout@v5

      - name: Set up JDK
        uses: actions/setup-java@v5
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'gradle'

      - name: Build
        run: ./gradlew build --no-daemon --stacktrace
